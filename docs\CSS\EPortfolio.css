/* General Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  scroll-behavior: smooth; /* Default smooth scrolling for browsers */
}

body {
  font-family: 'IBM Plex Sans', sans-serif;
  color: #333;
  display: flex;
  overflow-x: hidden;
  overflow-y: auto;
  background-color: #ffffff;
  scroll-behavior: smooth; /* Smooth scrolling across sections */
}

/* Container to hold sidebar and content */
.container {
  display: flex;
  width: 100%;
  min-height: 100vh;
  overflow-x: hidden;
  overflow-y: auto;
  scroll-snap-type: y mandatory; /* Enable vertical snap scrolling */
  scroll-behavior: smooth; /* Smooth scrolling across sections */
}

/* Header Styles */
.header {
  display: flex; /* Always show header */
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background-color: #111;
  color: #fff;
  z-index: 20; /* Ensure it stays on top */
  padding: 10px 20px;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

/* Burger Menu */
.burger-menu {
  display: none; /* Hidden by default */
  flex-direction: column;
  justify-content: space-between;
  cursor: pointer;
  width: 35px; /* Increased the width for better visibility */
  height: 25px; /* Increased the height to give space for the bars */
  z-index: 30; /* Ensure it's on top */
  position: relative; /* Position relative to the header */
}

.burger-menu div {
  width: 100%;
  height: 5px;
  background-color: #fff;
  transition: transform 0.3s ease, opacity 0.3s ease;
}

/* Animated Burger Menu (open state) */
.header.open .burger-menu {
  display: block;
}

.header.open .burger-menu div:nth-child(1) {
  transform: rotate(45deg);
  position: relative;
  top: 7px;
}

.header.open .burger-menu div:nth-child(2) {
  opacity: 0;
}

.header.open .burger-menu div:nth-child(3) {
  transform: rotate(-45deg);
  position: relative;
  top: -7px;
}

/* Mobile Navigation */
.mobile-nav {
  display: none;
  position: absolute;
  top: 100%;
  right: 0;
  background-color: #222;
  width: 100%;
  padding: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease;
}

.header.open .mobile-nav {
  display: block;
}

/* Mobile Navigation List */
.mobile-nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Mobile Navigation Links */
.mobile-nav ul li {
  margin: 15px 0;
}

.mobile-nav ul li a {
  color: #fff;
  text-decoration: none;
  font-size: 18px;
  font-weight: 500;
  transition: color 0.3s ease, transform 0.3s ease;
}

.mobile-nav ul li a:hover {
  color: orange;
  transform: translateX(5px);
}

/* Mobile and Tablet Devices */
@media screen and (max-width: 768px) {
  .header {
    display: flex; /* Always display header */
  }

  .burger-menu {
    display: flex; /* Show burger menu */
    position: absolute; /* Position it on the right */
    top: 50%; /* Vertically center it */
    right: 0px; /* Align it to the right edge */
    transform: translateY(-50%); /* Correct for vertical centering */
    z-index: 30; /* Ensure it's on top */
  }

  .header .mobile-nav {
    display: none;
  }

  .header.open .mobile-nav {
    display: block;
  }

  .header .title {
    font-size: 30px;
    font-weight: 800;
  }
}




/* Sidebar Styling */
.sidebar {
  width: 25%;
  background-color: #000;
  color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 40px;
  position: sticky;
  top: 0;
  height: 100vh;
  z-index: 10; /* Keep sidebar above content */
  transition: transform 0.3s ease-in-out; /* Smooth transition for sidebar visibility */
}

.logo {
  font-family: 'IBM Plex Serif', serif;
  font-size: 3rem;
  font-weight: 700;
  color: transparent;
  background: linear-gradient(25deg, #eee, #ffffff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  overflow: hidden;
  white-space: nowrap;
  border-right: 1px solid orange; /* Thinner cursor */
  animation: typewriter 4s steps(4) 1s 1 normal both, cursor 0.75s step-end infinite;
  position: relative;
  letter-spacing: 6.5px;
}

@keyframes typewriter {
  from {
    width: 0;
  }
  to {
    width: 70%;
  }
}

@keyframes cursor {
  50% {
    border-color: transparent;
  }
}

.sidebar nav ul {
  list-style: none;
}

.sidebar nav ul li {
  margin-bottom: 20px;
}

/* Sidebar Navigation Links */
.sidebar nav ul li a {
  font-family: 'IBM Plex Sans', sans-serif;
  color: #fff;
  text-decoration: none;
  font-size: 16px;
  font-weight: 400;
  position: relative;
  transition: color 0.3s ease, transform 0.5s ease, font-weight 0s ease; /* All properties transition */
}

.sidebar nav ul li a.active {
  color: orange;
  font-weight: 600; /* A less abrupt change in weight */
  transform: scale(1.05); /* Slightly scale the active link */
}

.sidebar nav ul li a:hover {
  color: orange;
  padding-left: 20px;
  transition: background-color 0.3s, font-weight 0.3s, padding-left 0.3s;
}

.sidebar nav ul li a.active:hover::after {
  transform: scaleX(1);
  transform-origin: bottom left;
}

.social-links {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: -50px;
}

.social-links .icon {
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #e0e0e0, #f5f5f5);
  color: #000;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  text-decoration: none;
  font-size: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease, background 0.3s ease, color 0.3s ease;
}

.social-links .icon:hover {
  background: linear-gradient(135deg, orange, #feb47b);
  color: #fff;
  transform: scale(1.2);
  box-shadow: 0 8px 12px rgba(0, 0, 0, 0.2);
}

.social-links .icon:not(:hover) {
  transform: scale(1);
}

.social-links .icon:active {
  transform: scale(1);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.copyright {
  font-family: 'IBM Plex Sans', sans-serif;
  font-size: 16px;
  color: #fff;
  text-align: center;
}

/* Burger Menu Button */
.burger-menu {
  display: none; /* Hidden by default */
  cursor: pointer;
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 11; /* Ensure the burger menu is on top of content */
}

.burger-menu div {
  width: 30px;
  height: 4px;
  background-color: #fff;
  margin: 6px 0;
  transition: 0.4s;
}

.burger-menu.open div:nth-child(1) {
  transform: rotate(-45deg);
  top: 10px;
}

.burger-menu.open div:nth-child(2) {
  opacity: 0;
}

.burger-menu.open div:nth-child(3) {
  transform: rotate(45deg);
  top: -10px;
}

/* Responsive Styles */
@media screen and (max-width: 1200px) {
  .container {
    flex-direction: column; /* Stack sidebar and content vertically on medium screens */
  }

  .sidebar {
    width: 100%;
    height: auto;
    position: fixed;
    top: 0;
    left: -100%; /* Initially hidden off-screen */
    padding: 20px; /* Reduce padding */
  }

  .logo {
    font-size: 2.5rem; /* Smaller logo for better fit */
    letter-spacing: 4px;
  }

  /* Burger menu visible on smaller screens */
  .burger-menu {
    display: block;
  }
}

@media screen and (max-width: 992px) {
  .sidebar {
    width: 100%;
    height: auto;
    position: fixed;
    top: 0;
    left: -100%;
    padding: 15px; /* Further reduce padding */
  }

  .logo {
    font-size: 2rem; /* Smaller logo */
    letter-spacing: 3px;
  }

  .sidebar nav ul li a {
    font-size: 14px; /* Smaller font size for links */
  }
}

@media screen and (max-width: 768px) {
  .container {
    flex-direction: column;
  }

  .sidebar {
    padding: 10px;
  }

  .logo {
    font-size: 1.5rem; /* Even smaller logo */
    letter-spacing: 2px;
  }

  .sidebar nav ul li a {
    font-size: 12px; /* Smaller text for links */
  }

  .social-links .icon {
    width: 35px;
    height: 35px;
    font-size: 18px; /* Smaller icons */
  }

  .copyright {
    font-size: 14px; /* Smaller copyright text */
  }

  /* Hide sidebar in mobile view */
  .sidebar {
    display: none;
  }

  /* Show hamburger menu in mobile view */
  .burger-menu {
    display: block;
  }
}

@media screen and (max-width: 480px) {

  body {
    overflow: auto;
  }

  .container {
    overflow: hidden;
    scroll-snap-type: y mandatory;
  }

  /* Title and Burger Menu - Top Bar */
  .logo {
    font-size: 1.2rem;
    letter-spacing: 1.5px;
    position: relative;
    padding: 10px;
    width: 100%;
    background-color: #000;
    color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .burger-menu {
    display: block;
  }

  .sidebar {
    width: 100%;
    padding: 10px;
    position: fixed;
    top: 0;
    left: -100%;
  }

  .sidebar nav ul li a {
    font-size: 12px; /* Even smaller text for links */
  }

  .social-links .icon {
    width: 30px;
    height: 30px;
    font-size: 16px; /* Even smaller icons */
  }

  .copyright {
    font-size: 12px; /* Smaller copyright text */
  }
}



/* Main Content Area */
main {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  height: 100vh;
  scroll-snap-type: y mandatory;
  scroll-behavior: smooth;
}

/* Full Page Sections */
.page {
  width: 100%;
  min-height: 100vh; /* Make the page take full screen height */
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  scroll-snap-align: start; /* Snap the page content to the top */
}

/* Hero Content */
.hero-content {
  max-width: 50%;
  margin-left: 80px;
}

.hero-content h2 {
  font-family: 'IBM Plex Sans', sans-serif;
  font-size: 80px;
  font-weight: 200;
  margin-bottom: 12px;
  color: rgb(0, 0, 0);
  line-height: 0.9;
}

.hero-content h2 .name-part {
  font-weight: 300;
}

.hero-content h2 .is {
  font-weight: 300;
}

.hero-content h2 .leandro {
  font-weight: 800;
  background: linear-gradient(45deg, rgb(255, 153, 0), rgb(255, 196, 70), orange);
  -webkit-background-clip: text; /* For Safari */
  background-clip: text; /* For modern browsers */
  color: transparent; /* Makes the text transparent so the gradient shows through */
}

.hero-content h2 .name {
  font-weight: 800;
  background: linear-gradient(45deg, rgb(255, 166, 0), rgb(255, 196, 70), orange);
  -webkit-background-clip: text; /* For Safari */
  background-clip: text; /* For modern browsers */
  color: transparent; /* Makes the text transparent so the gradient shows through */
}

.hero-content h2 {
  animation: fadeIn 1.5s ease-in-out;
}

.hero-content p {
  font-family: 'IBM Plex Sans', sans-serif;
  font-size: 24px;
  font-style: italic;
  margin-bottom: 20px;
  color: rgb(0, 0, 0);
}

.hero-content .role {
  font-weight: 800;
}

.hero-content .school {
  font-weight: 400;
}

.cta-button {
  display: inline-flex;
  align-items: center;
  padding: 10px 20px;
  background-color: rgb(0, 0, 0);
  color: #fff;
  text-decoration: none;
  font-weight: 500;
  font-family: 'IBM Plex Sans', sans-serif;
  font-size: 16px;
  margin-bottom: 20px;
  position: relative;
  border-radius: 5px;
  transition: background-color 0.3s, transform 0.3s;
}

.cta-button:hover {
  background-color: orange;
  transform: scale(1.05);
}

.cta-button i {
  margin-left: 10px;
  font-size: 18px;
  transition: transform 0.3s;
}

.cta-button:hover i {
  transform: translateX(5px);
}

/* Contact Info */
.contact-info {
  font-weight: bold;
  font-family: 'IBM Plex Sans', sans-serif;
  color: #131313;
}

.contact-info span {
  display: block;
  font-size: 16px;
  font-style: italic;
  display: flex;
  align-items: center;
  gap: 8px;
}

.contact-info i {
  color: rgb(0, 0, 0);
  font-size: 18px;
  background-color: #dddddd;
  border-radius: 50%;
  padding: 10px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 36px;
  height: 36px;
}

.contact-item {
  cursor: pointer;
  text-decoration: none;
}

.contact-item:first-child {
  margin-top: 20px;
}

.hero-image {
  position: relative;
  width: 600px;
}

.hero-image img {
  width: 100%;
  border-radius: 10px;
}

/* Media Queries */

/* Tablet Devices (max-width: 1024px) */
@media (max-width: 1024px) {
  body {
    overflow: auto;
  }

  .hero-content {
    max-width: 70%;
    margin-left: 20px;
  }

  .hero-content h2 {
    font-size: 60px;
  }

  .hero-content p {
    font-size: 20px;
  }

  .cta-button {
    font-size: 14px;
    padding: 8px 16px;
  }

  .hero-image {
    width: 100%;
  }

  .page {
    flex-direction: column;
    padding: 15px;
    margin-top: 0px; /* Added margin to avoid header overlap */
  }

  /* Ensure the image is above the content on smaller screens */
  .hero-image {
    order: -1; /* Move image above the text */
  }

  .hero-content {
    margin-left: 0;
    text-align: center;
  }
}

/* Mobile Devices (max-width: 768px) */
@media (max-width: 768px) {

  body {
    overflow: auto;
  }

  .container {
    overflow: hidden;
    scroll-snap-type: y mandatory;
  }

  .hero-content {
    max-width: 90%;
    margin-left: 0;
    text-align: center;
  }

  .hero-content h2 {
    font-size: 40px;
    margin-bottom: 10px;
  }

  .hero-content p {
    font-size: 18px;
  }

  .cta-button {
    font-size: 14px;
    padding: 8px 16px;
  }

  .hero-image {
    width: 100%;
    margin-top: 110px;
    margin-bottom: 10px;
  }

  .page {
    flex-direction: column;
    padding: 10px;
    justify-content: flex-start;
    margin-top: 80px; /* Added margin to avoid header overlap */
  }

  /* Ensure the image is above the content on smaller screens */
  .hero-image {
    order: -1; /* Move image above the text */
  }

  .header {
    display: block; /* Show header on smaller screens */
  }

  .header .burger-menu, .header .title {
    display: block; /* Show burger menu and title in mobile view */
  }
}

/* Large Screens (min-width: 1200px) */
@media (min-width: 1200px) {
  .hero-content {
    max-width: 50%;
    margin-left: 80px;
  }

  .hero-content h2 {
    font-size: 80px;
  }

  .cta-button {
    font-size: 16px;
  }

  .page {
    padding: 20px;
  }
}

@media screen and (min-width: 769px) {
  .header .burger-menu, .header .title {
    display: none;
  }
}

@media screen and (min-width: 769px) {
  .header {
    display: none; /* Hide header on larger screens */
  }
}

/* Show header in mobile view */
@media screen and (max-width: 768px) {
  .header {
    display: block; /* Show header on smaller screens */
  }
}




/* About Section */
#about {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 80px 20px;
  background-color: #ffffff;
}

.about-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  gap: 50px;
  padding: 90px;
}

.profile-photo {
  flex: 1;
  display: flex;
  justify-content: center;
}

.profile-photo img {
  width: 400px;
  height: 400px;
}

.about-details {
  flex: 2;
  text-align: left;
  color: #333;
  font-family: 'IBM Plex Sans', sans-serif;
  line-height: 1.6;
}

.about-details h1 {
  font-family: 'Poppins', sans-serif;
  font-size: 42px;
  font-weight: 700;
  margin-bottom: 20px;
  color: #333;
}

.about-details h1 .highlight {
  background: linear-gradient(45deg, orange, rgb(255, 174, 0), rgb(241, 213, 133));
  -webkit-background-clip: text; /* For Safari */
  background-clip: text; /* For modern browsers */
  color: transparent; /* Makes the text transparent so the gradient shows through */
}

.about-details h2 {
  font-family: 'Poppins', sans-serif;
  font-size: 28px;
  color: orange;
  margin-bottom: 10px;
}

.about-details h3 {
  font-family: 'IBM Plex Sans', sans-serif;
  font-size: 20px;
  color: #555;
  margin-bottom: 20px;
}

.about-details p {
  font-family: 'IBM Plex Sans', sans-serif;
  font-size: 16px;
  color: #333;
  line-height: 1.8;
  margin-bottom: 30px;
  max-width: 800px;
  text-align: justify;
}

.statistics {
  display: flex;
  gap: 40px;
  margin-top: 40px;
  justify-content: center;
}

.statistics div {
  text-align: center;
}

.statistics h3 {
  font-family: 'Poppins', sans-serif;
  font-size: 45px;
  font-weight: 900;
  background: linear-gradient(45deg, orange, rgb(255, 174, 0), rgb(255, 255, 255));
  -webkit-background-clip: text; /* For Safari */
  background-clip: text; /* For modern browsers */
  color: transparent; /* Makes the text transparent so the gradient shows through */
  margin-bottom: 5px;
}

.statistics p {
  font-family: 'IBM Plex Sans', sans-serif;
  font-size: 14px;
  color: #555;
  text-align: center;
}

.cv-button {
  margin-top: 30px;
  justify-content: center; /* Center the button horizontally */
  text-align: center; /* Ensures that text within the button is centered */
}

.cv-button a {
  display: inline-flex;
  align-items: center;
  padding: 14px 30px;
  background-color: #1D2939;
  color: #fff;
  text-decoration: none;
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  font-weight: 600;
  border-radius: 5px;
  transition: background-color 0.3s, transform 0.3s;
}

.cv-button a:hover {
  background-color: orange;
  transform: scale(1.05);
}

.cv-button a i {
  margin-left: 10px;
  font-size: 18px;
  transition: transform 0.3s;
}

.cv-button a:hover i {
  transform: translateX(5px);
}

/* Media Queries */

/* Tablet Devices (max-width: 1024px) */
@media (max-width: 1024px) {
  .about-container {
    flex-direction: column;
    padding: 60px 20px;
    gap: 30px;
  }

  .profile-photo img {
    width: 300px;
    height: 300px;
    margin-top: 85px;
  }

  .about-details {
    flex: 1;
    text-align: center;
  }

  .about-details h1 {
    font-size: 36px;
  }

  .about-details h2 {
    font-size: 24px;
  }

  .about-details h3 {
    font-size: 18px;
  }

  .about-details p {
    font-size: 14px;
    max-width: 100%;
  }

  .statistics {
    display: flex; /* Ensures horizontal layout */
    justify-content: space-between; /* Spreads out the items */
    gap: 20px;
    flex-wrap: nowrap; /* Prevents wrapping */
  }

  .statistics div {
    flex: 1; /* Distributes space evenly */
    text-align: center;
  }

  .statistics h3 {
    font-size: 36px;
  }

  .cv-button {
    margin-top: 20px;
  }
}

/* Mobile Devices (max-width: 768px) */
@media (max-width: 768px) {
  #about {
    padding: 50px 15px;
  }

  .about-container {
    flex-direction: column;
    padding: 40px 10px;
    gap: 20px;
  }

  .profile-photo img {
    width: 250px;
    height: 250px;
  }

  .about-details {
    flex: 1;
    text-align: center;
  }

  .about-details h1 {
    font-size: 30px;
  }

  .about-details h2 {
    font-size: 22px;
  }

  .about-details h3 {
    font-size: 16px;
  }

  .about-details p {
    font-size: 14px;
    max-width: 100%;
    text-align: center;
  }

  .statistics {
    display: flex; /* Ensures horizontal layout */
    justify-content: space-between; /* Spreads out the items */
    gap: 20px;
    flex-wrap: nowrap; /* Prevents wrapping */
  }

  .statistics div {
    flex: 1; /* Distributes space evenly */
    text-align: center;
  }

  .statistics h3 {
    font-size: 28px;
  }

  .cv-button {
    margin-top: 20px;
    display: none;
  }
}




/* General Layout for Desktop/Laptop */
#background {
  padding: 64px 32px;
  background-color: #ffffff;
}

.content {
  display: flex;
  justify-content: center;
  gap: 10px;
  flex-wrap: wrap;
  margin-top: 3rem;
}

.section {
  width: 100%;
  max-width: 45%;
  margin-bottom: 2.5rem;
  padding: 2rem;
  background-color: #fff;
  border-radius: 12px;
}

.section h3 {
  font-size: 1.875rem;
  font-weight: 600;
  color: #1D2939;
  margin-bottom: 1.25rem;
}

.section p {
  font-size: 1.125rem;
  color: #4A5568;
  text-align: justify;
}

.services {
  display: grid;
  grid-template-columns: repeat(2, 1fr); /* Two items per row */
  gap: 1rem;
  margin-top: 1.5rem;
}

.service {
  display: flex;
  flex-direction: column;
  justify-content: center; /* Vertically center the content */
  align-items: center; /* Horizontally center the content */
  height: 240px; /* Fixed height for cards */
  background-color: #ffffff;
  border-radius: 12px;
  text-align: center;
  position: relative;  /* Ensure the background and text/icons are positioned correctly */
  transition: transform 0.3s ease-in-out;
}

.service-link {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
  background-color: #f1f1f1;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
  color: #000;
  text-decoration: none;
  height: 100%;
  z-index: 2;  /* Ensure the text and icon are above the background circle by default */
  transition: all 0.3s ease; /* Smooth transition effect */
}

.service-link:hover {
  color: white;  /* Make the text and icon white on hover */
  z-index: 3;  /* Ensure the whole service card content is above the background */
}

/* Hover effect for h4, p, and icon */
.service-link:hover h4,
.service-link:hover p,
.service-link:hover i {
  color: white; /* Change text and icon color to white on hover */
}

/* Icon settings */
.service i {
  font-size: 2rem;
  color: orange;  /* Icon is orange by default */
  margin-bottom: 0.5rem; /* Reduced bottom margin */
  z-index: 2;  /* Ensure the icon stays above the background */
}

/* Service Background circle settings */
.service-bg {
  height: 90px;
  width: 90px;
  background-color: #f9b234;
  z-index: 0;  /* Ensure the background stays behind everything else */
  position: absolute;
  top: -50px;
  right: -50px;
  border-radius: 50%;
  transition: all 0.5s ease; /* Smooth transition for background */
}

.service-link:hover .service-bg {
  transform: scale(10); /* Make the background circle scale on hover */
}

/* Text styling */
.service h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1D2939;  /* Title color by default */
  z-index: 2;  /* Ensure the title is above the background */
}

.service p {
  font-size: 1rem;
  color: #4A5568;  /* Paragraph color by default */
  margin-top: 5px;
  z-index: 2;  /* Ensure the paragraph is above the background */
}

/* Margin adjustments for sections */
.with-margin {
  margin-bottom: 10px; /* Adjust as needed */
}

.with-margin-2 {
  margin-bottom: 125px; /* Adjusted for the last child */
}

.skills {
  margin-top: 1.5rem;
}

.skill {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
}

.skill-name {
  width: 110px;
  font-weight: 600;
  color: #4A5568;
  font-size: 1.125rem;
  margin-right: 1rem;
}

.skill-bar {
  flex: 1;
  background-color: #e2e8f0; /* Lighter background */
  border-radius: 30px;
  height: 10px;
  position: relative;
  overflow: hidden;
}

.skill-level {
  background-color: #f39c12; /* Example color */
  height: 100%;
  width: 0%;
  border-radius: 20px;
  transition: width 1s ease-out; /* Smooth transition when width changes */
}

.skill-level.animate {
  transition: width 1s ease-out;
}

.right-section {
  background-color: #ffffff;
}

/* Tablet Devices (max-width: 1024px) */
@media only screen and (max-width: 1024px) {
  .content {
    flex-direction: column;
    align-items: center;
  }

  .section {
    max-width: 100%;
  }

  .section h3 {
    font-size: 1.625rem;
  }

  .services {
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* Two items per row on tablet */
    gap: 1rem;
    margin-top: 1.5rem;
  }

  .service {
    flex-basis: 100%;
    height: auto; /* Allow the card to expand vertically on small screens */
  }

  .service h4 {
    font-size: 24px;
  }

  .service p {
    font-size: 14px;
  }
}

/* Mobile Devices (max-width: 768px) */
@media only screen and (max-width: 768px) {
  .content {
    flex-direction: column;
    align-items: center;
  }

  .section {
    max-width: 100%;
  }

  .section h3 {
    font-size: 1.5rem;
  }

  .services {
    grid-template-columns: 1fr; /* Stack the services vertically on small screens */
    padding: 20px;
  }

  .service {
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    height: auto; /* Allow the card to expand vertically on small screens */
  }

  .service h4 {
    font-size: 22px;
  }

  .service p {
    font-size: 14px;
  }

  .service-link {
    padding: 15px; /* Reduce padding for mobile screens */
  }

  .service-bg {
    width: 70px; /* Smaller background circle on mobile */
    height: 70px;
  }
}
/* Media Query for Tablet Devices (max-width: 1024px) */
@media only screen and (max-width: 1024px) {
  .content {
    flex-direction: column;
    align-items: center;
  }

  .section {
    max-width: 100%;
  }

  .section h3 {
    font-size: 1.625rem;
  }

  .services {
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* Two items per row on tablet */
    gap: 1rem;
    margin-top: 1.5rem;
  }

  .service {
    flex-basis: 100%;
    height: auto; /* Allow the card to expand vertically on small screens */
  }

  .service h4 {
    font-size: 24px;
  }

  .service p {
    font-size: 14px;
  }
}

/* Mobile Devices (max-width: 768px) */
@media only screen and (max-width: 768px) {
  .content {
    flex-direction: column;
    align-items: center;
  }

  .section {
    max-width: 100%;
    display: flex;  /* Enable flexbox for centering content */
    flex-direction: column; /* Stack items vertically */
    align-items: center; /* Center content horizontally */
    justify-content: center; /* Center content vertically */
    text-align: center; /* Align text center */
  }

  .section h3 {
    margin-top: -50px;
    font-size: 1.5rem;
    align-items: center;
    justify-content: center;
    justify-items: center;
    justify-self: center;
    font-size: 40px;
    font-weight: 900px;
  }

  .services {
    margin-top: -20px;
    grid-template-columns: 1fr; /* Stack the services vertically on small screens */
    padding: 20px;

  }

  .service {
    -ms-flex-preferred-size: 100%; /* Ensure it works on IE10+ */
    flex-basis: 100%;
    height: auto;
  }

  .service-link {
    padding: 20px;
  }

  .service i {
    font-size: 2rem;
  }

  .service h4 {
    font-size: 20px;
  }

  .service p {
    font-size: 14px;
  }
}

@media (max-width: 768px) {
  .skills {
    margin-top: 1rem;
    padding: 0 20px; /* Add padding for smaller screens */
    display: none;
  }

  .skill {
    display: none;
    flex-direction: column; /* Stack skill name and bar vertically */
    align-items: flex-start; /* Align skill name to the left */
    margin-bottom: 1rem; /* Reduced margin for better spacing */
  }

  .skill-name {
    width: 100%; /* Full width for mobile */
    margin-right: 0;
    font-size: 1rem; /* Smaller font size for mobile */
    display: none;
  }

  .skill-bar {
    width: 100%; /* Ensure the bar takes full width */
    margin-top: 0.5rem; /* Small margin between name and skill bar */
    display: none;
  }

  .skill-level {
    transition: width 0.8s ease-out; /* Slightly faster transition on mobile */
    display: none;
  }

  .right-section {
    display: none;
  }
}

/* For Tablet (above 768px and below 1024px) */
@media (max-width: 1024px) {
  .skills {
    padding: 0 30px; /* Some padding for tablets */
  }

  .skill {
    display: flex;
    flex-direction: column; /* Stack skill name and bar vertically */
    align-items: flex-start;
    margin-bottom: 1.5rem; /* Standard margin */
  }

  .skill-name {
    width: 100%;
    font-size: 1.125rem; /* Maintain font size for tablets */
  }

  .skill-bar {
    width: 100%; /* Full width */
    margin-top: 0.5rem;
  }
}




/* Projects Section */
/* Base Styles (Desktop/PC) */
#projects {
  background-color: #f9f9f9;
  padding: 60px 0;
}

.projects-content {
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
}

.projects-content h2 {
  font-size: 42px;
  margin-bottom: 10px;
  font-weight: bold;
  color: #000000;
}

.projects-content p {
  font-size: 18px;
  margin-bottom: 25px;
  color: #7c7c7c;
}

.project-slider {
  position: relative;
  max-width: 100%;
  overflow: hidden;
}

.slider {
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.slides__wrapper {
  display: flex;
  transition: transform 0.5s ease-in-out;
  position: relative;
  width: 100%;
}

.slides {
  display: flex;
  transition: transform 0.5s ease-in-out;
}

.slide {
  min-width: 100%; /* Each slide takes full width of the container */
  transition: opacity 0.3s ease-in-out;
  box-sizing: border-box;
  position: relative;
}

.slide img {
  width: 100%; /* Make the image take full width of the container */
  height: 520px; /* Set a fixed height to make the images uniform */
  object-fit: cover; /* Ensure the image covers the space without distortion */
  border-radius: 12px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.slide-info {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #272626;
  background: rgba(255, 255, 255, 0.6);
  padding: 20px 25px;
  border-radius: 12px;
  width: 80%;
  max-width: 300px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}

.slide-info h3 {
  font-size: 26px;
  margin-bottom: 12px;
  font-weight: bold;
}

.slide-info p {
  font-weight: 300;
  font-size: 16px;
  margin-bottom: 18px;
  color: #272626;
}

.project-link {
  color: #fff;
  text-decoration: none;
  font-weight: 500;
  background: linear-gradient(135deg, rgb(247, 186, 73), orange); /* Lighter gradient */
  padding: 12px 24px;
  border-radius: 8px;
  transition: background-color 0.3s ease, transform 0.3s ease;
  display: inline-block;
}

.project-link:hover {
  background: linear-gradient(135deg, rgb(199, 150, 58), rgb(202, 132, 2)); /* Lighter on hover */
  transform: scale(1.05);
}

.slider--btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgb(255, 255, 255); /* Lighter background */
  border: none;
  color: #333; /* Darker icon color for better contrast */
  padding: 15px;
  border-radius: 50%;
  cursor: pointer;
  transition: background-color 0.3s ease;
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
}

.slider--btn svg {
  width: 24px;
  height: 24px;
  fill: #000000;
}

.slider--btn:hover {
  background-color: rgb(233, 233, 233);
}

.slider--btn__prev {
  left: 20px;
}

.slider--btn__next {
  right: 20px;
}

/* Responsive Design for Tablets and Mobiles */
@media (max-width: 768px) {
  .projects-content h2 {
    margin-top: 90px;
    font-size: 36px; /* Slightly smaller heading for tablets */
  }

  .projects-content p {
    font-size: 16px; /* Reduced font size for better readability on tablets */
  }

  .slide-info {
    width: 90%; /* Increased width to fill more space on tablets */
  }

  .slide-info h3 {
    font-size: 22px; /* Slightly smaller heading for mobile/tablet */
  }

  .slide-info p {
    font-size: 14px; /* Smaller text for mobile/tablet */
  }

  .project-link {
    font-size: 14px; /* Smaller link size for mobile/tablet */
  }

  .slider--btn {
    width: 40px;
    height: 40px;
    padding: 10px;
  }

  .slider--btn svg {
    width: 18px;
    height: 18px;
  }
}

@media (max-width: 480px) {
  .projects-content h2 {
    font-size: 30px; /* Smaller heading for smaller screens */
  }

  .projects-content p {
    font-size: 14px; /* Even smaller text for mobile */
  }

  .slide-info {
    width: 95%; /* Further increased width on mobile */
    padding: 15px 20px; /* Reduced padding for mobile */
  }

  .slide-info h3 {
    font-size: 20px; /* Smaller heading for mobile */
  }

  .slide-info p {
    font-size: 12px; /* Even smaller text on mobile */
  }

  .project-link {
    font-size: 12px; /* Even smaller link size on mobile */
    padding: 8px 16px; /* Adjusted padding for mobile */
  }

  .slider--btn {
    width: 35px;
    height: 35px;
    padding: 8px;
  }

  .slider--btn svg {
    width: 16px;
    height: 16px;
  }
}



/* Contact Section */
#contact {
  background-color: #f4f4f9;
  padding: 60px 0;
}

.contact-content {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
  padding: 0 15px;
}

.contact-content h2 {
  font-size: 36px;
  margin-bottom: 20px;
  font-weight: bold;
  color: #333;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.contact-content p {
  font-size: 16px;
  margin-bottom: 40px;
  color: #666;
  line-height: 1.6;
}

.contact-form {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 600px;
  margin: 0 auto;
  padding: 30px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.form-group {
  width: 100%;
  margin-bottom: 20px;
  text-align: left;
}

.form-group label {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  display: block;
  font-weight: 500;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 15px;
  font-size: 16px;
  border: 1px solid #ccc;
  border-radius: 8px;
  outline: none;
  transition: all 0.3s ease;
  background-color: #f9f9f9;
  color: #333;
}

.form-group input:focus,
.form-group textarea:focus {
  border-color: #f39c12;
  background-color: #fff;
  box-shadow: 0 0 5px rgba(243, 156, 18, 0.6);
}

.form-group textarea {
  height: 150px;
  resize: vertical;
}

.submit-btn {
  background-color: #f39c12;
  color: white;
  padding: 14px 28px;
  border-radius: 50px;
  font-size: 16px;
  cursor: pointer;
  border: none;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: 0 4px 8px rgba(243, 156, 18, 0.2);
  font-family: 'IBM Plex Sans', sans-serif;
  font-weight: 600;
}

.submit-btn:hover {
  background-color: #e67e22;
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(243, 156, 18, 0.3);
}

@media (max-width: 768px) {
  .contact-content h2 {
    margin-top: 110px;
    font-size: 32px;
  }

  .contact-content p {
    font-size: 14px;
  }

  .contact-form {
    width: 100%;
    padding: 20px;
  }

  .form-group input,
  .form-group textarea {
    font-size: 14px;
  }

  .submit-btn {
    font-size: 14px;
    padding: 12px 24px;
  }
}

@media (max-width: 480px) {
  .contact-content h2 {
    font-size: 28px;
  }

  .contact-content p {
    font-size: 14px;
  }

  .contact-form {
    width: 90%;
    padding: 15px;
  }

  .form-group input,
  .form-group textarea {
    font-size: 12px;
  }

  .submit-btn {
    font-size: 14px;
    padding: 10px 20px;
  }
}




.toast {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: orange; /* Bright green for positive action */
  color: #fff;
  padding: 15px 25px;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* Subtle shadow for depth */
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  font-weight: 600;
  text-align: center;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease, transform 0.3s ease;
  transform: translateY(20px); /* Slight upward animation */
}

.toast.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.toast i {
  margin-right: 10px; /* Space between icon and text */
  font-size: 16px;
}

.toast .cta-save {
  margin-top: 10px;
  display: inline-block;
  padding: 8px 15px;
  background-color: #fff;
  color: orange;
  border-radius: 5px;
  font-size: 13px;
  font-weight: bold;
  text-decoration: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: background-color 0.2s, color 0.2s;
}

.toast .cta-save:hover {
  background-color: orange;
  color: #fff;
}

@media screen and (max-width: 768px) {
  html, body {
    overflow: auto; /* Enable scrolling for smaller devices */
  }

  .container {
    overflow-y: scroll; /* Ensure scrolling on mobile */
    scroll-snap-type: y mandatory; /* Retain snapping */
  }
}

/* For tablet devices */
@media screen and (max-width: 1024px) {
  html, body {
    overflow: auto; /* Enable scrolling for smaller devices */
  }

  .container {
    overflow-y: scroll; /* Ensure scrolling on mobile */
    scroll-snap-type: y mandatory; /* Retain snapping */
  }
}